{"@t":"2025-07-29T09:07:06.7566669Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:07:07.1017687Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:07:07.2746530Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:07:07.2851469Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:07:07.8972510Z","@mt":"Now listening on: {address}","address":"https://localhost:57851","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:07:07.9022993Z","@mt":"Now listening on: {address}","address":"http://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:07:08.0243274Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:07:08.0277124Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:07:08.0309926Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:22:09.3482386Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:22:09.4823494Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:22:09.6261445Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:22:09.6307101Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:22:10.0387468Z","@mt":"Now listening on: {address}","address":"https://localhost:57851","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:22:10.0441218Z","@mt":"Now listening on: {address}","address":"http://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:22:10.1147035Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:22:10.1171820Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:22:10.1192279Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:22:18.2683903Z","@mt":"首次请求触发端点刷新","@tr":"b2bacb0fce71761cdfd0f6115f8d834f","@sp":"52f91e6d058d7795","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEEG5KRL4RT:00000001","RequestPath":"/","ConnectionId":"0HNEEG5KRL4RT","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:22:18.2789681Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"b2bacb0fce71761cdfd0f6115f8d834f","@sp":"52f91e6d058d7795","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEEG5KRL4RT:00000001","RequestPath":"/","ConnectionId":"0HNEEG5KRL4RT","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:27.9494526Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:28.0710630Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:28.1930170Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:28.1969353Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:28.5711730Z","@mt":"Now listening on: {address}","address":"https://localhost:57851","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:28.5810567Z","@mt":"Now listening on: {address}","address":"http://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:28.6613598Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:28.6650893Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:28.6678999Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:53.0966203Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:53.2209057Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:53.3314826Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:53.3366327Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:53.7178790Z","@mt":"Now listening on: {address}","address":"https://localhost:57851","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:53.7215698Z","@mt":"Now listening on: {address}","address":"http://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:53.8034337Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:53.8060979Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:23:53.8092662Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:24:00.7527061Z","@mt":"首次请求触发端点刷新","@tr":"3c469dcc4081a885504815decf9195a8","@sp":"3f30621d1948dca2","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEEG6JFGPA2:00000001","RequestPath":"/","ConnectionId":"0HNEEG6JFGPA2","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:24:00.7676161Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"3c469dcc4081a885504815decf9195a8","@sp":"3f30621d1948dca2","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEEG6JFGPA2:00000001","RequestPath":"/","ConnectionId":"0HNEEG6JFGPA2","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:24:08.7542511Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"3478716bd52700921f1edca70c645597","@sp":"f14fe6e489bc3af4","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEEG6JFGPA2:0000000F","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEEG6JFGPA2","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:24:08.7582958Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"3478716bd52700921f1edca70c645597","@sp":"f14fe6e489bc3af4","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEEG6JFGPA2:0000000F","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEEG6JFGPA2","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:24:08.7702053Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"3478716bd52700921f1edca70c645597","@sp":"f14fe6e489bc3af4","PathCount":12,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEEG6JFGPA2:0000000F","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEEG6JFGPA2","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:29:26.5123727Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:29:26.6596615Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:29:26.7805408Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:29:26.7845804Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:29:27.2285253Z","@mt":"Now listening on: {address}","address":"https://localhost:57851","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:29:27.2318089Z","@mt":"Now listening on: {address}","address":"http://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:29:27.2967717Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:29:27.2999146Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:29:27.3024380Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:30:13.8047270Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:30:13.9588918Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:30:14.0827283Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:30:14.0891856Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:30:14.4676034Z","@mt":"Now listening on: {address}","address":"https://localhost:57851","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:30:14.4718326Z","@mt":"Now listening on: {address}","address":"http://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:30:14.5489367Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:30:14.5511892Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:30:14.5533502Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:05.0542880Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:05.1224237Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:05.1922514Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:05.1958255Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:05.3227227Z","@mt":"Now listening on: {address}","address":"http://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:05.3250184Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:05.3261845Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:05.3274928Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:52.5304933Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:52.5851321Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:52.6578264Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:52.6617504Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:52.7977200Z","@mt":"Now listening on: {address}","address":"http://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:52.7997362Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:52.8008046Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:33:52.8020172Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:00.2665130Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:00.4107079Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:00.5481571Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:00.5532483Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:00.9037595Z","@mt":"Now listening on: {address}","address":"https://localhost:57851","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:00.9145958Z","@mt":"Now listening on: {address}","address":"http://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:00.9722169Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:00.9759770Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:00.9792349Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:02.8089546Z","@mt":"首次请求触发端点刷新","@tr":"b82a6ed96b1c284f8389d89f1f85b582","@sp":"e16b2ed51c65af84","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEEGEE33O99:00000001","RequestPath":"//scalar/v1","ConnectionId":"0HNEEGEE33O99","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:02.8266022Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"b82a6ed96b1c284f8389d89f1f85b582","@sp":"e16b2ed51c65af84","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEEGEE33O99:00000001","RequestPath":"//scalar/v1","ConnectionId":"0HNEEGEE33O99","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:30.2553370Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:30.3940268Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:30.5293492Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:30.5336065Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:30.9774958Z","@mt":"Now listening on: {address}","address":"https://localhost:57851","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:30.9819276Z","@mt":"Now listening on: {address}","address":"http://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:31.0665690Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:31.0693702Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:31.0718983Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:32.7694122Z","@mt":"首次请求触发端点刷新","@tr":"4d80fae6d27ed70f7834991b906fbf94","@sp":"a0549be2a7484774","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEEGEN0PCQK:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEEGEN0PCQK","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:32.7798007Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"4d80fae6d27ed70f7834991b906fbf94","@sp":"a0549be2a7484774","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEEGEN0PCQK:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEEGEN0PCQK","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:33.2364870Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"83624d38ace474edc3657fdeff832331","@sp":"d4a9c1c4d8ad0095","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEEGEN0PCQK:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEEGEN0PCQK","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:33.2412457Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"83624d38ace474edc3657fdeff832331","@sp":"d4a9c1c4d8ad0095","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEEGEN0PCQK:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEEGEN0PCQK","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:38:33.2553402Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"83624d38ace474edc3657fdeff832331","@sp":"d4a9c1c4d8ad0095","PathCount":12,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEEGEN0PCQK:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEEGEN0PCQK","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:50:04.7028988Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:50:04.8675212Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:50:05.0080813Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:50:05.0122515Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:50:05.3993791Z","@mt":"Now listening on: {address}","address":"https://localhost:57851","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:50:05.4038795Z","@mt":"Now listening on: {address}","address":"http://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:50:05.5843513Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:50:05.5868793Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:50:05.5901204Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:50:07.7247799Z","@mt":"首次请求触发端点刷新","@tr":"4953355dde4edb8e09dbe78b054c01e3","@sp":"3b5842a47aa6af9f","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEEGL605K1S:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEEGL605K1S","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:50:07.7364262Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"4953355dde4edb8e09dbe78b054c01e3","@sp":"3b5842a47aa6af9f","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEEGL605K1S:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEEGL605K1S","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:50:08.2137802Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"aa392887f7fd2cde6f31d919b83c1a66","@sp":"cb28fa8cb2dfd858","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEEGL605K1S:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEEGL605K1S","MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:50:08.2187183Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"aa392887f7fd2cde6f31d919b83c1a66","@sp":"cb28fa8cb2dfd858","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEEGL605K1S:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEEGL605K1S","MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:50:08.2308359Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"aa392887f7fd2cde6f31d919b83c1a66","@sp":"cb28fa8cb2dfd858","PathCount":12,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEEGL605K1S:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEEGL605K1S","MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:55:03.1156809Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:55:03.2317791Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:55:03.3468333Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:55:03.3511190Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:55:03.7047932Z","@mt":"Now listening on: {address}","address":"https://localhost:57851","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:55:03.7091226Z","@mt":"Now listening on: {address}","address":"http://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:55:03.8877893Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:55:03.8900285Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:55:03.8921103Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:55:05.8423550Z","@mt":"首次请求触发端点刷新","@tr":"f87e5c4742f4e7c3adfd0fb384900ad2","@sp":"0276e4b86bce2767","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEEGNUS7FK9:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEEGNUS7FK9","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:55:05.8533021Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"f87e5c4742f4e7c3adfd0fb384900ad2","@sp":"0276e4b86bce2767","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEEGNUS7FK9:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEEGNUS7FK9","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:55:06.3383394Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"dcc4da50b20ebb237dd4c2e10ab1d7ba","@sp":"3df1e040cd5a151a","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEEGNUS7FK9:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEEGNUS7FK9","MachineName":"DESKTOP-O211UJ1","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:55:06.3430779Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"dcc4da50b20ebb237dd4c2e10ab1d7ba","@sp":"3df1e040cd5a151a","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEEGNUS7FK9:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEEGNUS7FK9","MachineName":"DESKTOP-O211UJ1","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:55:06.3549593Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"dcc4da50b20ebb237dd4c2e10ab1d7ba","@sp":"3df1e040cd5a151a","PathCount":12,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEEGNUS7FK9:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEEGNUS7FK9","MachineName":"DESKTOP-O211UJ1","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:56:32.0013557Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:56:32.1295998Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:56:32.3300101Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:56:32.3340691Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:56:32.7361649Z","@mt":"Now listening on: {address}","address":"https://localhost:57851","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:56:32.7408693Z","@mt":"Now listening on: {address}","address":"http://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:56:33.1501059Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:56:33.1533975Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:56:33.1559235Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:56:34.5153828Z","@mt":"首次请求触发端点刷新","@tr":"30a594f95c3c3e2e91fa197d7a07b75b","@sp":"9f65622e5f13397b","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEEGOPAD0FC:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEEGOPAD0FC","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:56:34.6584814Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"30a594f95c3c3e2e91fa197d7a07b75b","@sp":"9f65622e5f13397b","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEEGOPAD0FC:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEEGOPAD0FC","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:56:35.7610028Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidOperationException: Action 'SSIC.Modules.Auth.Controllers.RoleController.GetRoles (SSIC.Modules.Auth)' does not have an attribute route. Action methods on controllers annotated with ApiControllerAttribute must be attribute routed.\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.EnsureActionIsAttributeRouted(ActionModel actionModel)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.OnProvidersExecuting(ApplicationModelProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApplicationModelFactory.CreateApplicationModel(IEnumerable`1 controllerTypes)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.GetDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.OnProvidersExecuting(ActionDescriptorProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.UpdateCollection()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.get_ActionDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescriptionGroupCollectionProvider.get_ApiDescriptionGroups()\r\n   at Microsoft.AspNetCore.OpenApi.OpenApiDocumentService.GetOpenApiPathsAsync(HashSet`1 capturedTags, IServiceProvider scopedServiceProvider, IOpenApiOperationTransformer[] operationTransformers, IOpenApiSchemaTransformer[] schemaTransformers, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.OpenApi.OpenApiDocumentService.GetOpenApiDocumentAsync(IServiceProvider scopedServiceProvider, HttpRequest httpRequest, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Builder.OpenApiEndpointRouteBuilderExtensions.<>c__DisplayClass0_0.<<MapOpenApi>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Http.Generated.<GeneratedRouteBuilderExtensions_g>F56B68D2B55B5B7B373BA2E4796D897848BC0F04A969B1AF6260183E8B9E0BAF2__GeneratedRouteBuilderExtensionsCore.<>c__DisplayClass2_0.<<MapGet0>g__RequestHandler|5>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 94\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"4b63735c7f3211f63ab57298e45e1f14","@sp":"325254bfb5219bcc","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNEEGOPAD0FC:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEEGOPAD0FC","MachineName":"DESKTOP-O211UJ1","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:57:46.8966676Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:57:47.0366221Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:57:47.2917038Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:57:47.3012931Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:57:48.0428010Z","@mt":"Now listening on: {address}","address":"https://localhost:57851","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:57:48.0465172Z","@mt":"Now listening on: {address}","address":"http://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:57:48.2608636Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:57:48.2639944Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:57:48.2662887Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:57:48.8696829Z","@mt":"首次请求触发端点刷新","@tr":"91954ae17d65e0b4f431536a498cc60f","@sp":"4b03e2342859f704","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEEGPFL8C5E:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEEGPFL8C5E","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:57:49.0215903Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"91954ae17d65e0b4f431536a498cc60f","@sp":"4b03e2342859f704","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEEGPFL8C5E:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEEGPFL8C5E","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T09:57:49.8913414Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.InvalidOperationException: Action 'SSIC.Modules.Auth.Controllers.AuthController.Login (SSIC.Modules.Auth)' does not have an attribute route. Action methods on controllers annotated with ApiControllerAttribute must be attribute routed.\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.EnsureActionIsAttributeRouted(ActionModel actionModel)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApiBehaviorApplicationModelProvider.OnProvidersExecuting(ApplicationModelProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ApplicationModelFactory.CreateApplicationModel(IEnumerable`1 controllerTypes)\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.GetDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApplicationModels.ControllerActionDescriptorProvider.OnProvidersExecuting(ActionDescriptorProviderContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.UpdateCollection()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.Initialize()\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.DefaultActionDescriptorCollectionProvider.get_ActionDescriptors()\r\n   at Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescriptionGroupCollectionProvider.get_ApiDescriptionGroups()\r\n   at Microsoft.AspNetCore.OpenApi.OpenApiDocumentService.GetOpenApiPathsAsync(HashSet`1 capturedTags, IServiceProvider scopedServiceProvider, IOpenApiOperationTransformer[] operationTransformers, IOpenApiSchemaTransformer[] schemaTransformers, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.OpenApi.OpenApiDocumentService.GetOpenApiDocumentAsync(IServiceProvider scopedServiceProvider, HttpRequest httpRequest, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Builder.OpenApiEndpointRouteBuilderExtensions.<>c__DisplayClass0_0.<<MapOpenApi>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Http.Generated.<GeneratedRouteBuilderExtensions_g>F56B68D2B55B5B7B373BA2E4796D897848BC0F04A969B1AF6260183E8B9E0BAF2__GeneratedRouteBuilderExtensionsCore.<>c__DisplayClass2_0.<<MapGet0>g__RequestHandler|5>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 94\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware.InvokeAsync(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\Endpoints\\EndpointRefreshMiddleware.cs:line 73\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in F:\\SSIC\\Host\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"6d17bdc01eb6013b3d9be7405ec5dbcc","@sp":"106e9cf658d9553a","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNEEGPFL8C5E:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEEGPFL8C5E","MachineName":"DESKTOP-O211UJ1","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T10:00:31.7624703Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T10:00:31.8760253Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T10:00:32.0155194Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T10:00:32.0230612Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T10:00:32.3706846Z","@mt":"Now listening on: {address}","address":"https://localhost:57851","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T10:00:32.3779319Z","@mt":"Now listening on: {address}","address":"http://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T10:00:32.4530628Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T10:00:32.4563591Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T10:00:32.4588678Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T10:00:33.7248053Z","@mt":"首次请求触发端点刷新","@tr":"542ff0fd96bafa5c1e1007a64ae67e52","@sp":"bdaa91fed3b19595","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEEGR0Q74TV:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEEGR0Q74TV","MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T10:00:33.7363731Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"542ff0fd96bafa5c1e1007a64ae67e52","@sp":"bdaa91fed3b19595","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEEGR0Q74TV:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEEGR0Q74TV","MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T10:00:34.3537381Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"790aa6779390d8759e78805142b44eaf","@sp":"4abf48e4919db05a","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEEGR0Q74TV:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEEGR0Q74TV","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T10:00:34.3590417Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"790aa6779390d8759e78805142b44eaf","@sp":"4abf48e4919db05a","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEEGR0Q74TV:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEEGR0Q74TV","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-29T10:00:34.3707544Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"790aa6779390d8759e78805142b44eaf","@sp":"4abf48e4919db05a","PathCount":7,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEEGR0Q74TV:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEEGR0Q74TV","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
