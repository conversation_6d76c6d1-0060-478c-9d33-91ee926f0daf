using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;

namespace SSIC.Modules.Auth.Controllers
{
    /// <summary>
    /// 角色管理控制器
    /// </summary>
    [ApiController]
    // [Authorize] // 暂时注释掉用于测试路由
    public class RoleController : ControllerBase
    {
        private readonly ILogger<RoleController> _logger;

        // 模拟角色数据
        private static readonly List<Role> _roles = new()
        {
            new Role { Id = 1, Name = "Admin", Description = "系统管理员", CreatedAt = DateTime.Now.AddDays(-30) },
            new Role { Id = 2, Name = "User", Description = "普通用户", CreatedAt = DateTime.Now.AddDays(-20) },
            new Role { Id = 3, Name = "Manager", Description = "管理员", CreatedAt = DateTime.Now.AddDays(-10) }
        };

        public RoleController(ILogger<RoleController> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 获取所有角色
        /// </summary>
        /// <returns>角色列表</returns>
        [HttpGet]
        public IActionResult GetRoles()
        {
            try
            {
                _logger?.LogInformation("获取所有角色");
                return Ok(new { success = true, data = _roles, total = _roles.Count, message = "获取角色列表成功" });
            }
            catch (Exception ex)
            {
                return Ok(new { success = false, message = $"获取角色列表失败: {ex.Message}" });
            }
        }

        /// <summary>
        /// 测试方法 - 简单返回字符串
        /// </summary>
        /// <returns>测试消息</returns>
        [HttpGet]
        public IActionResult Test()
        {
            return Ok(new { success = true, message = "RoleController 测试成功！", timestamp = DateTime.Now });
        }

        /// <summary>
        /// 根据ID获取角色
        /// </summary>
        /// <param name="id">角色ID</param>
        /// <returns>角色信息</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetRole(int id)
        {
            _logger.LogInformation("获取角色信息: {RoleId}", id);
            
            var role = _roles.FirstOrDefault(r => r.Id == id);
            if (role == null)
            {
                return NotFound(new { message = "角色不存在" });
            }

            return Ok(new { success = true, data = role });
        }

        /// <summary>
        /// 创建新角色
        /// </summary>
        /// <param name="request">创建角色请求</param>
        /// <returns>创建结果</returns>
        [HttpPost]
        public async Task<IActionResult> CreateRole([FromBody] CreateRoleRequest request)
        {
            _logger.LogInformation("创建新角色: {RoleName}", request.Name);
            
            if (string.IsNullOrEmpty(request.Name))
            {
                return BadRequest(new { message = "角色名称不能为空" });
            }

            if (_roles.Any(r => r.Name.Equals(request.Name, StringComparison.OrdinalIgnoreCase)))
            {
                return BadRequest(new { message = "角色名称已存在" });
            }

            var newRole = new Role
            {
                Id = _roles.Max(r => r.Id) + 1,
                Name = request.Name,
                Description = request.Description,
                CreatedAt = DateTime.Now
            };

            _roles.Add(newRole);

            return CreatedAtAction(nameof(GetRole), new { id = newRole.Id }, 
                new { success = true, data = newRole, message = "角色创建成功" });
        }

        /// <summary>
        /// 更新角色
        /// </summary>
        /// <param name="id">角色ID</param>
        /// <param name="request">更新角色请求</param>
        /// <returns>更新结果</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateRole(int id, [FromBody] UpdateRoleRequest request)
        {
            _logger.LogInformation("更新角色: {RoleId}", id);
            
            var role = _roles.FirstOrDefault(r => r.Id == id);
            if (role == null)
            {
                return NotFound(new { message = "角色不存在" });
            }

            if (string.IsNullOrEmpty(request.Name))
            {
                return BadRequest(new { message = "角色名称不能为空" });
            }

            if (_roles.Any(r => r.Id != id && r.Name.Equals(request.Name, StringComparison.OrdinalIgnoreCase)))
            {
                return BadRequest(new { message = "角色名称已存在" });
            }

            role.Name = request.Name;
            role.Description = request.Description;
            role.UpdatedAt = DateTime.Now;

            return Ok(new { success = true, data = role, message = "角色更新成功" });
        }

        /// <summary>
        /// 删除角色
        /// </summary>
        /// <param name="id">角色ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteRole(int id)
        {
            _logger.LogInformation("删除角色: {RoleId}", id);
            
            var role = _roles.FirstOrDefault(r => r.Id == id);
            if (role == null)
            {
                return NotFound(new { message = "角色不存在" });
            }

            _roles.Remove(role);

            return Ok(new { success = true, message = "角色删除成功" });
        }

        /// <summary>
        /// 获取角色权限
        /// </summary>
        /// <param name="id">角色ID</param>
        /// <returns>权限列表</returns>
        [HttpGet("{id}/permissions")]
        public async Task<IActionResult> GetRolePermissions(int id)
        {
            _logger.LogInformation("获取角色权限: {RoleId}", id);
            
            var role = _roles.FirstOrDefault(r => r.Id == id);
            if (role == null)
            {
                return NotFound(new { message = "角色不存在" });
            }

            // 模拟权限数据
            var permissions = new[]
            {
                new { id = 1, name = "用户管理", code = "user.manage" },
                new { id = 2, name = "角色管理", code = "role.manage" },
                new { id = 3, name = "系统设置", code = "system.setting" }
            };

            return Ok(new { success = true, data = permissions });
        }
    }

    #region 模型定义

    /// <summary>
    /// 角色模型
    /// </summary>
    public class Role
    {
        /// <summary>
        /// 角色ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 角色名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 角色描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedAt { get; set; }
    }

    /// <summary>
    /// 创建角色请求模型
    /// </summary>
    public class CreateRoleRequest
    {
        /// <summary>
        /// 角色名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 角色描述
        /// </summary>
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// 更新角色请求模型
    /// </summary>
    public class UpdateRoleRequest
    {
        /// <summary>
        /// 角色名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 角色描述
        /// </summary>
        public string Description { get; set; } = string.Empty;
    }

    #endregion
}
