﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;

namespace SSIC.Modules.Auth.Controllers
{
    /// <summary>
    /// 用户认证控制器
    /// </summary>
    [ApiController]
    public class AuthController : ControllerBase
    {
        private readonly ILogger<AuthController> _logger;

        public AuthController(ILogger<AuthController> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 用户登录
        /// </summary>
        /// <param name="request">登录请求</param>
        /// <returns>登录结果</returns>
        [HttpPost]
        public async Task<IActionResult> Login([FromBody] LoginRequest request)
        {
            _logger.LogInformation("用户登录请求: {Username}", request.Username);
            
            // 模拟登录验证逻辑
            if (string.IsNullOrEmpty(request.Username) || string.IsNullOrEmpty(request.Password))
            {
                return BadRequest(new { message = "用户名和密码不能为空" });
            }

            // 模拟验证成功
            if (request.Username == "admin" && request.Password == "123456")
            {
                var token = GenerateToken(request.Username);
                return Ok(new LoginResponse
                {
                    Success = true,
                    Message = "登录成功",
                    Token = token,
                    Username = request.Username,
                    ExpiresAt = DateTime.Now.AddHours(24)
                });
            }

            return Unauthorized(new { message = "用户名或密码错误" });
        }

        /// <summary>
        /// 用户注册
        /// </summary>
        /// <param name="request">注册请求</param>
        /// <returns>注册结果</returns>
        [HttpPost]
        public async Task<IActionResult> Register([FromBody] RegisterRequest request)
        {
            _logger.LogInformation("用户注册请求: {Username}", request.Username);
            
            if (string.IsNullOrEmpty(request.Username) || string.IsNullOrEmpty(request.Password))
            {
                return BadRequest(new { message = "用户名和密码不能为空" });
            }

            if (request.Password != request.ConfirmPassword)
            {
                return BadRequest(new { message = "两次输入的密码不一致" });
            }

            // 模拟注册逻辑
            return Ok(new { 
                success = true, 
                message = "注册成功", 
                username = request.Username 
            });
        }

        /// <summary>
        /// 获取当前用户信息
        /// </summary>
        /// <returns>用户信息</returns>
        [HttpGet]
        [Authorize]
        public async Task<IActionResult> GetProfile()
        {
            var username = User.Identity?.Name ?? "anonymous";
            _logger.LogInformation("获取用户信息: {Username}", username);
            
            return Ok(new UserProfile
            {
                Username = username,
                Email = $"{username}@example.com",
                Role = "User",
                LastLoginTime = DateTime.Now.AddHours(-2)
            });
        }

        /// <summary>
        /// 用户登出
        /// </summary>
        /// <returns>登出结果</returns>
        [HttpPost]
        [Authorize]
        public async Task<IActionResult> Logout()
        {
            var username = User.Identity?.Name ?? "anonymous";
            _logger.LogInformation("用户登出: {Username}", username);
            
            return Ok(new { success = true, message = "登出成功" });
        }

        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="request">修改密码请求</param>
        /// <returns>修改结果</returns>
        [HttpPost]
        [Authorize]
        public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordRequest request)
        {
            var username = User.Identity?.Name ?? "anonymous";
            _logger.LogInformation("用户修改密码: {Username}", username);
            
            if (string.IsNullOrEmpty(request.OldPassword) || string.IsNullOrEmpty(request.NewPassword))
            {
                return BadRequest(new { message = "旧密码和新密码不能为空" });
            }

            if (request.NewPassword != request.ConfirmNewPassword)
            {
                return BadRequest(new { message = "两次输入的新密码不一致" });
            }

            // 模拟密码修改逻辑
            return Ok(new { success = true, message = "密码修改成功" });
        }

        /// <summary>
        /// 生成模拟Token
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>Token</returns>
        private string GenerateToken(string username)
        {
            return $"mock_token_{username}_{DateTime.Now.Ticks}";
        }
    }

    #region 请求和响应模型

    /// <summary>
    /// 登录请求模型
    /// </summary>
    public class LoginRequest
    {
        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 密码
        /// </summary>
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// 记住我
        /// </summary>
        public bool RememberMe { get; set; } = false;
    }

    /// <summary>
    /// 登录响应模型
    /// </summary>
    public class LoginResponse
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 访问令牌
        /// </summary>
        public string Token { get; set; } = string.Empty;

        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime ExpiresAt { get; set; }
    }

    /// <summary>
    /// 注册请求模型
    /// </summary>
    public class RegisterRequest
    {
        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 密码
        /// </summary>
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// 确认密码
        /// </summary>
        public string ConfirmPassword { get; set; } = string.Empty;

        /// <summary>
        /// 邮箱
        /// </summary>
        public string Email { get; set; } = string.Empty;
    }

    /// <summary>
    /// 用户信息模型
    /// </summary>
    public class UserProfile
    {
        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 邮箱
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 角色
        /// </summary>
        public string Role { get; set; } = string.Empty;

        /// <summary>
        /// 最后登录时间
        /// </summary>
        public DateTime LastLoginTime { get; set; }
    }

    /// <summary>
    /// 修改密码请求模型
    /// </summary>
    public class ChangePasswordRequest
    {
        /// <summary>
        /// 旧密码
        /// </summary>
        public string OldPassword { get; set; } = string.Empty;

        /// <summary>
        /// 新密码
        /// </summary>
        public string NewPassword { get; set; } = string.Empty;

        /// <summary>
        /// 确认新密码
        /// </summary>
        public string ConfirmNewPassword { get; set; } = string.Empty;
    }

    #endregion
}
