[{"ContainingType": "SSIC.Modules.Auth.Controllers.AuthController", "Method": "ChangePassword", "RelativePath": "api/auth/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SSIC.Modules.Auth.Controllers.ChangePasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SSIC.Modules.Auth.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SSIC.Modules.Auth.Controllers.LoginRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SSIC.Modules.Auth.Controllers.AuthController", "Method": "Logout", "RelativePath": "api/auth/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SSIC.Modules.Auth.Controllers.AuthController", "Method": "GetProfile", "RelativePath": "api/auth/profile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SSIC.Modules.Auth.Controllers.AuthController", "Method": "Register", "RelativePath": "api/auth/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SSIC.Modules.Auth.Controllers.RegisterRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SSIC.Modules.Auth.Controllers.RoleController", "Method": "GetRoles", "RelativePath": "api/auth/role", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SSIC.Modules.Auth.Controllers.RoleController", "Method": "CreateRole", "RelativePath": "api/auth/role", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SSIC.Modules.Auth.Controllers.CreateRoleRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SSIC.Modules.Auth.Controllers.RoleController", "Method": "GetRole", "RelativePath": "api/auth/role/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SSIC.Modules.Auth.Controllers.RoleController", "Method": "UpdateRole", "RelativePath": "api/auth/role/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "SSIC.Modules.Auth.Controllers.UpdateRoleRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SSIC.Modules.Auth.Controllers.RoleController", "Method": "DeleteRole", "RelativePath": "api/auth/role/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SSIC.Modules.Auth.Controllers.RoleController", "Method": "GetRolePermissions", "RelativePath": "api/auth/role/{id}/permissions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SSIC.Modules.Auth.Controllers.RoleController", "Method": "Test", "RelativePath": "api/auth/role/test", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SSIC.Infrastructure.Startups.HostBuilder+<>c__DisplayClass4_0", "Method": "<Configure>b__3", "RelativePath": "api/endpoints/map-module-route", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "SSIC.Infrastructure.Startups.HostBuilder+<>c__DisplayClass4_0", "Method": "<Configure>b__2", "RelativePath": "api/endpoints/refresh", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "SSIC.Infrastructure.Startups.HostBuilder+<>c__DisplayClass4_0", "Method": "<Configure>b__4", "RelativePath": "api/openapi/refresh", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}], "Tags": ["System"], "Summary": "手动刷新OpenAPI文档"}]